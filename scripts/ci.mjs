#!/usr/bin/env zx
/* eslint-disable no-console */
import inquirer from 'inquirer';
import { load } from 'js-toml';
import 'zx/globals';
import { apps } from './meta.js';

let app = process.argv[3];
if (!app) {
  const answer = await inquirer.prompt([
    {
      type: 'list',
      name: 'app',
      message: '⊙ 选择项目：',
      choices: apps,
    },
  ]);
  app = answer.app;
}

const file = fs.readFileSync(`apps/${app}/ci.toml`, { encoding: 'utf-8' });
const conf = load(file);
console.log('conf', conf);

// 内网 ping 检查
const testUrl = 'http://rainbond.gzdevops.tsintergy.com';
try {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 5000);
  await fetch(testUrl, {
    method: 'GET',
    cache: 'no-store',
    signal: controller.signal,
  });
  clearTimeout(timeoutId);
} catch (error) {
  console.log(chalk.yellow(`内网 ping 检查超时: ${testUrl}`));
  process.exit(1);
}
// 检查 docker daemon 启动
await $`docker info`;
// 构建 app
await $`pnpm build ${app}`;
// 切换目录
cd(`apps/${app}`);
await $`chmod -R 755 dist`;
// 写入 Dockerfile
await $`echo -e ${conf.Dockerfile} > Dockerfile`;
await $`echo -e '*\n!dist' > .dockerignore`;
// 构建并推送 docker 镜像
const registry = 'registry.tsintergy.com';
// 兼容本地 ci.toml 写有 tsintergy/ 前缀的情况
const docker_image_name = String(conf.docker_image_name).replace(/^tsintergy\//, '');
const docker_image_tag = conf.docker_image_tag ?? '1.0.0';
const docker_image = `${registry}/tsintergy/${docker_image_name}:${docker_image_tag}`;
await $`docker build --platform=linux/amd64 . -t ${docker_image}`;
await $`docker login ${registry} -u dev -p Qinghua123@`;
await $`docker push ${docker_image}`;
await $`docker logout ${registry}`;
// 清除
await $`docker rmi ${docker_image}`;
await $`rm Dockerfile .dockerignore`;
// 触发 Rainbond 部署
if (conf.rainbond_deploy_trigger) {
  await $`curl -d '{\"secret_key\": \"${conf.rainbond_deploy_key}\"}' -H \"Content-type: application/json\" -X POST ${conf.rainbond_deploy_api}`;
}
