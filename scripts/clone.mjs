#!/usr/bin/env zx
import inquirer from 'inquirer';
import 'zx/globals';
import { packagesStandAlone } from './meta.js';

const ACCESS_TOKEN = process.env.GITLAB_TOKEN ?? '********************';
const gitUrl = await $`git ls-remote --get-url origin`;
const url = new URL(gitUrl);
const appGroup = 'pps/frontend-monoliths/apps';
const packageGroup = 'pps/frontend-monoliths/packages';

let app = process.argv[3];
if (!app) {
  // https://docs.gitlab.com/ee/api/groups.html#list-a-groups-projects
  const repos = await (
    await fetch(
      `${url.origin}/api/v4/groups/${encodeURIComponent(
        appGroup,
      )}/projects?per_page=100&with_shared=false`,
      {
        headers: {
          'PRIVATE-TOKEN': ACCESS_TOKEN,
        },
      },
    )
  ).json();
  const apps = repos.map((item) => item.name);
  const answers = await inquirer.prompt([
    {
      type: 'list',
      name: 'app',
      message: '⊙ 初始化app：',
      choices: apps,
    },
  ]);
  app = answers.app;
}

// 下载共享仓库
for (const pkg of packagesStandAlone) {
  if (!fs.pathExistsSync(`packages/${pkg}`)) {
    await $`git submodule add -f ${url.origin}/${packageGroup}/${pkg} packages/${pkg}`;
  }
}

// 下载子项目仓库
await $`git submodule add -f ${url.origin}/${appGroup}/${app} apps/${app}`;

// unstage submodule changes
await $`git reset HEAD`;
