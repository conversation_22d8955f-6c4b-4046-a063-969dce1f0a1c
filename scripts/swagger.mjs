#!/usr/bin/env zx
/* eslint-disable no-console */
import inquirer from 'inquirer';
import 'zx/globals';
import { apps } from './meta.js';

// -----------------------------------------------------------------------------
// swagger to typescript
// -----------------------------------------------------------------------------

let app = process.argv[3];
if (!app) {
  const answer = await inquirer.prompt([
    {
      type: 'list',
      name: 'app',
      message: '⊙ 选择项目：',
      choices: apps,
    },
  ]);
  app = answer.app;
}

let swaggerApiUrl = process.argv[4];
if (!swaggerApiUrl) {
  const answer = await inquirer.prompt([
    {
      type: 'input',
      name: 'swaggerApiUrl',
      message: '> 输入swagger文档地址',
    },
  ]);
  swaggerApiUrl = answer.swaggerApiUrl;
}

if (swaggerApiUrl.includes('/doc.html')) {
  const baseUrl = swaggerApiUrl.split('/doc.html')[0];
  swaggerApiUrl = `${baseUrl}/v2/api-docs?group=tsie`;
}
const outPath = `apps/${app}/src/types/swagger.ts`;
await $`npx openapi-typescript ${swaggerApiUrl} -o ${outPath}`;
