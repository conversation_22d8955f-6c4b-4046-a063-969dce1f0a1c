import { load } from 'cheerio';
import { writeFileSync, readFileSync } from 'fs';

function injectEnv2Html(path, envs) {
  try {
    writeFileSync(
      path,
      (function injectENV() {
        const $ = load(fs.readFileSync(path).toString());
        const scriptEle = `<script>${Object.keys(envs)
          .map((envKey) => {
            return `window.${envKey}=${JSON.stringify(envs[envKey])}`;
          })
          .join(';')}</script>`;
        $('head').append(scriptEle);
        return $.html();
      })(),
    );
  } catch (e) {
    console.log(e);
  }
}

export { injectEnv2Html };
