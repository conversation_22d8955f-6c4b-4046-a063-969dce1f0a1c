#!/usr/bin/env zx
import inquirer from 'inquirer';
import 'zx/globals';
import { workspaces } from './meta.js';

/**
 * 简化 pnpm --filter 命令，将包名输入改成了单选
 *
 * 这个脚本其实没什么必要🤡，原生命令更灵活更强大，请直接使用 pnpm --filter 命令(https://pnpm.io/zh/filtering)
 *
 * 使用示例：
 *
 * 打印 package 路径
 * 方式一：先输入 pnpm filter，选择项目后再输入 exec pwd
 * 方式二：先输入 pnpm filter sx-group，再输入 exec pwd
 * 方式三：直接输入 pnpm filter sx-group exec pwd
 *
 * 子项目添加依赖
 * 先输入 pnpm filter，选择项目后再输入 add lodash
 *
 * 子项目删除依赖
 * 先输入 pnpm filter，选择项目后再输入 rm lodash
 */

let project = process.argv[3];
if (!project) {
  const answer = await inquirer.prompt([
    {
      type: 'list',
      name: 'project',
      message: '⊙ 选择项目：',
      choices: workspaces,
    },
  ]);
  project = answer.project;
}

let commandArgs = process.argv.slice(4);
if (commandArgs.length === 0) {
  const { command } = await inquirer.prompt([
    {
      type: 'input',
      name: 'command',
      message: '> 输入命令：',
    },
  ]);
  commandArgs = command.split(' ').filter(Boolean);
}

$.spawn('pnpm', ['--filter', project, ...commandArgs], {
  stdio: 'inherit',
});
