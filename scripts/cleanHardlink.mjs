#!/usr/bin/env zx
/* eslint-disable no-console */
import 'zx/globals';
import { apps } from './meta.js';

for (const app of apps) {
  try {
    const storeDir = `apps/${app}/node_modules/.pnpm`;
    if (fs.pathExistsSync(storeDir)) {
      const dirs = fs.readdirSync(storeDir);
      const links = dirs.filter((v) => /^file\+/.test(v));
      for (const link of links) {
        const linkPath = `${storeDir}/${link}`;
        fs.rmdirSync(linkPath, { recursive: true });
        console.log(`删除硬链接：${linkPath}`);
      }
    }
  } catch (error) {
    console.error(error);
  }
}
