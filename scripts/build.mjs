#!/usr/bin/env zx
import inquirer from 'inquirer';
import 'zx/globals';
import { injectEnv2Html } from './htmlInject.mjs';
import { apps } from './meta.js';

let project = process.argv[3];
if (!project) {
  const answer = await inquirer.prompt([
    {
      type: 'list',
      name: 'project',
      message: '⊙ 选择项目：',
      choices: apps,
    },
  ]);
  project = answer.project;
}

const version = Date.now();

await $`raiden version build --target ${path.resolve(
  process.cwd(),
  `./apps/${project}/public`,
)} --versionNo ${version}`;

const childProcess = $.spawn(
  process.platform === 'win32' ? 'cross-env.cmd' : 'cross-env',
  [
    // ---
    `PROJECT_KEY=${project}`,
    `turbo`,
    `run`,
    `build`,
    `--env-mode=loose`,
    `--filter='./apps/${project}'`,
    ...process.argv.slice(4),
  ],
  {
    stdio: 'inherit',
  },
);

childProcess.on('close', async (code) => {
  if (code === 0) {
    await $`rm ./apps/${project}/public/release-version.json`;
    injectEnv2Html(path.resolve(process.cwd(), `./apps/${project}/dist/index.html`), {
      versionInfo: version,
    });
  }
});

childProcess.on('exit', (code) => {
  if (code) {
    process.exit(code);
  }
});
