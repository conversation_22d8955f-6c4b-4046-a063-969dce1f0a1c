const { generateTheme } = require('antd-theme-generator');
const path = require('path');

const options = {
	// antd的路径
  // antDir: '../packages/adssSxGroup/node_modules/antd',
  antDir: '../node_modules/antd',
  // antDir: path.join(__dirname, '../node_modules/antd'),
	// 处理颜色相关less样式的目录
  stylesDir: path.join(__dirname, '../packages/adssSxGroup/src'),
	// 样式变量文件
  varFile: path.join(__dirname, '../packages/adssSxGroup/src/assets/less/cssModules.less'),
	// 需要抽离的颜色变量，默认是@primary-color，可自行添加其他的变量
  themeVariables: ['@primary-color'],
  // 输出路径，这里输出到public，命名为color.less，也可以自定义命名，但需要
  // 和下文引入link的名字对应。
  outputFilePath: path.join(__dirname, '../packages/adssSxGroup/src/assets/less/color.less'),
};

// 生成样式
generateTheme(options)
  .then((less) => {
    console.log('Theme generated successfully');
  })
  .catch((error) => {
    console.log('Error', error.message);
  });
