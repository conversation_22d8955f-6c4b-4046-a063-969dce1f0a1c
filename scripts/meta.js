const fg = require('fast-glob');

// 代理地址
const proxyUrlMap = {
  测试环境: 'https://adssx-test-gzdevops3.tsintergy.com/',
  国电投测试环境: 'http://gdt.test.gzdevops3.tsintergy.com/',
  // 国电投内网: 'https://www.dljyfzjc.spic.com.cn:20280/pct1/',
  mock: '',
  嘉鑫: 'http://172.31.103.102:12251/',
  nice哥: 'http://172.31.102.236:8083/',
  燕君: 'http://172.31.0.95:8080/',
  荣锋: 'http://172.31.103.24:14551/',
  '胤霖:8080': 'http://192.168.103.37:8080/',
  '胤霖:8081': 'http://192.168.103.37:8081/',
  绍斌: 'http://192.168.101.126:8084/',
  荣昌集团: 'http://172.31.102.27:8081/',
  荣昌火电: 'http://172.31.102.27:8082/',
  杰敏火电: 'http://172.31.102.75:8080/',
  杰敏集团: 'http://172.31.102.75:8081/',
  张震火电: 'http://172.31.102.199:8080/',
  张震集团: 'http://172.31.102.199:8081/',
  钟家豪: 'http://172.31.103.6:8080/',
  汪琪: 'http://172.31.103.98:8080/',
  锦涛: 'http://172.31.101.240:8082/',
};

// apps 目录内容
const apps = fg.sync('*', { cwd: 'apps', onlyDirectories: true });
// 单独管理的包
const packagesStandAlone = ['ppss', 'pps-style'];
// 与根仓库一起管理的包
const packagesWithRoot = ['preset-umi3', 'geo-json'];
// packages 目录内容
const packages = [...packagesStandAlone, ...packagesWithRoot];
// pnpm workspaces
const workspaces = [...apps, ...packages];

module.exports = {
  proxyUrlMap,
  apps,
  packagesStandAlone,
  packagesWithRoot,
  packages,
  workspaces,
};
