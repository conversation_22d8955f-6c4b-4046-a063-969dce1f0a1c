#!/usr/bin/env zx
/* eslint-disable no-console */
import inquirer from 'inquirer';
import 'zx/globals';
import { apps, proxyUrlMap } from './meta.js';

let project = process.argv[3];
if (!project) {
  const answer = await inquirer.prompt([
    {
      type: 'list',
      name: 'project',
      message: '⊙ 选择项目：',
      choices: apps,
    },
  ]);
  project = answer.project;
}

let proxy = '';
if (project !== 'ppss') {
  const answers = await inquirer.prompt([
    {
      type: 'list',
      name: 'proxy',
      message: '⊙ 选择接口代理：',
      choices: Object.keys(proxyUrlMap),
    },
  ]);
  proxy = answers.proxy;
}

const proxyUrl = proxyUrlMap[proxy];
const mock = proxy === 'mock' ? 'yes' : 'none';
const https = proxyUrl.includes('https://') ? 'yes' : '';

$.spawn(
  process.platform === 'win32' ? 'cross-env.cmd' : 'cross-env',
  [
    // ---
    `PROJECT_KEY=${project}`,
    `PROXY_URL=${proxyUrl}`,
    `MOCK=${mock}`,
    `HTTPS=${https}`,
    `FORCE_COLOR=2`,
    `turbo`,
    `run`,
    `dev`,
    `--env-mode=loose`,
    `--filter='./apps/${project}'`,
    ...process.argv.slice(4),
  ],
  {
    stdio: 'inherit',
  },
);
