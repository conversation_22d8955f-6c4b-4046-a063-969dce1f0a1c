#!/usr/bin/env zx
/* eslint-disable no-console */
import chalk from 'chalk';
import fg from 'fast-glob';
import 'zx/globals';
import { apps, packagesStandAlone } from './meta.js';

const { dim } = chalk;

async function deleteIntermediateFiles() {
  await $`pnpm -r --include-workspace-root exec rimraf dist src/.umi* .turbo`;
  console.log(dim`已删除工程中间文件`);
}

// .git/submodules 文件夹对齐
async function syncSubmoduleFolders() {
  const sourcePackages = fg.sync('*', { cwd: '.git/modules/packages', onlyDirectories: true });
  const sourceApps = fg.sync('*', { cwd: '.git/modules/apps', onlyDirectories: true });
  async function syncSubmoduleFolder(folder, sourceModules, distModules) {
    for (const name of sourceModules) {
      if (!distModules.includes(name)) {
        await $`rimraf .git/modules/${folder}/${name}`;
      }
    }
  }
  await syncSubmoduleFolder('packages', sourcePackages, packagesStandAlone);
  await syncSubmoduleFolder('apps', sourceApps, apps);
}

// .gitmodules 文件对齐
async function syncSubmoduleFile() {
  async function assembleFile(folder, modules, content) {
    let acc = content;
    for (const name of modules) {
      acc += `[submodule "${folder}/${name}"]
  path = ${folder}/${name}
  url = http://git.tsintergy.com:8070/pps/frontend-monoliths/${folder}/${name}\n`;
    }
    return acc;
  }
  let content = '';
  content = await assembleFile('packages', packagesStandAlone, content);
  content = await assembleFile('apps', apps, content);
  await fs.writeFile('.gitmodules', content);
}

async function syncSubmodules() {
  await syncSubmoduleFolders();
  await syncSubmoduleFile();
  console.log(dim`已对齐submodules`);
}

await deleteIntermediateFiles();
await syncSubmodules();
