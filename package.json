{"name": "root", "version": "2.0.0", "description": "", "private": true, "scripts": {"clone": "zx scripts/clone.mjs", "clone:all": "zx scripts/cloneAll.mjs", "dev": "zx scripts/dev.mjs", "build": "zx scripts/build.mjs", "build@pct1": "zx scripts/<EMAIL>", "ci": "zx scripts/ci.mjs", "swagger": "zx scripts/swagger.mjs", "release:ppss": "pnpm -C packages/ppss release", "test": "npm run select npm run test", "filter": "zx scripts/filter.mjs", "clean": "zx scripts/clean.mjs", "clean:nm": "zx scripts/cleanNodeModules.mjs", "reset": "pnpm clean && pnpm clean:nm", "postinstall": "husky install && chmod 755 .husky/*", "color": "node ./scripts/color.js"}, "author": "", "license": "ISC", "engines": {"node": ">=16.13.0", "pnpm": ">=7.0.0 <=7.23.0"}, "dependencies": {"@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@tsintergy/eslint-config-react": "^1.1.3", "@tsintergy/raiden": "^0.1.0", "@umijs/lint": "^4.0.13", "chalk": "^4.0.0", "cheerio": "1.0.0-rc.12", "cross-env": "^7.0.3", "eslint": "8.x", "fast-glob": "^3.2.11", "husky": "^6.0.0", "inquirer": "^8.2.0", "js-toml": "^1.0.0", "lint-staged": "^11.0.0", "openapi-typescript": "^5.0.0", "prettier": "2.x", "rimraf": "^3.0.2", "stylelint": "15.x", "stylelint-config-css-modules": "^4.2.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^33.0.0", "turbo": "~2.0.12", "typescript": "~4.3.0", "zx": "^7.0.8"}, "resolutions": {"eslint": "8.x", "eslint-plugin-unicorn": "55.0.0", "prettier": "2.x", "stylelint": "15.x", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.x", "@typescript-eslint/parser": "^6.x"}}