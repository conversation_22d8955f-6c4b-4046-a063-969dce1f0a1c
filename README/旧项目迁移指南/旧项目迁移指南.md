# 旧项目迁移指南

以蒙西火电项目 mx-fire 为例



1 新建远端仓库

![img](./img/Snipaste_2022-09-16_16-07-49.jpg)
![img](./img/Snipaste_2022-09-16_16-12-41.jpg)



2 复制旧项目代码过来

![img](./img/Snipaste_2022-09-16_16-40-54.jpg)



3 初始化仓库

```bash
cd packages/mx-fire
git init
git add --all
git commit -m "feat: init"
```



4 修改代码

4.1 首先修改 .gitmodules 文件

![img](./img/Snipaste_2022-09-16_16-56-38.jpg)

然后按照蒙西火电项目的这个提交去添加及修改文件 <http://git.tsintergy.com:8070/pps/frontend-monoliths/mx-fire/commit/b7bbf07d6f7d7dd55ecadd6f5b588b26c006754f>，项目的 src/assets/less 文件如果确定没用了也可以删掉。建议 `pnpm clone mx-fire`，然后在本地 IDE 中复制相关修改


4.2 项目不要出现类似 `import { useSelector } from '@/.umi/plugin-dva/exports'` 的代码，不然打包时会报错，应该写成 `import { useSelector } from 'umi'`。



5 尝试启动项目

```bash
pnpm dev
```



6 推送仓库

```bash
git remote add origin http://git.tsintergy.com:8070/pps/frontend-monoliths/mx-fire
git push --set-upstream origin master
```
