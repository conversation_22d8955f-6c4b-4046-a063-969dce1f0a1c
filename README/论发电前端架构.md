# 论发电前端架构

## 背景

无论是发电侧还是电网侧，都有个明显的需求，就是一个项目可能要做出定制化修改，提供给不同的客户。这带来一个还没有最佳方案的软件工程管理问题——项目间业务代码复用。



## 项目间代码复用

项目间代码复用的困难来自于两个方面：康威定律、工程实践。

#### 康威定律

介绍：[康威定律（转载） - 知乎 (zhihu.com)](https://zhuanlan.zhihu.com/p/359452953)

康威定律的核心思想在于公司的组织结构会影响到软件的设计和开发。要想在软件开发过程中实现代码复用，需要在不同的项目之间进行合理的分工和协作，同时采用适当的软件设计和开发方法。

结论：

1. 我们面临第一定律和第三定律的轻度困扰，但还没到项目间完全隔离独立的地步，存在代码复用的尝试空间
2. 受第二定律影响，项目的很多代码是复制粘贴出来的。很难说复制粘贴是错的，因为首先要保证工作能做完，其次才是工程合理

#### 工程实践

对于工程实践，大致有以下几种：

1. 将复用代码抽取到 npm 包。对于非业务代码可行，但对于业务代码修改效率太低了
2. 业务组件物料仓库。需要建立某种物料中心，其本质是复制粘贴，但这个过程实践起来非常麻烦，很难维持
3. 微前端。感觉比较适合大团队做完全隔离的项目，比如美团、淘宝里面的各种子应用。我们可以简单尝试，但滥用肯定会让事情变得很糟糕
4. Git 多分支或 worktree 管理多项目。不是一个可行的方案，随着项目演变很难相互合并代码
5. Git 多仓库管理多项目。通过 submodule / subtree 拆分为多仓库但统一管理
6. Monorepo。有各种各样的方案并且在不断改进中，但现阶段的方案都是面向 npm 包级别的管理，而不是面向项目级别的管理
7. Turborepo。一个为 monorepo 提供了依赖图解析、任务编排、internal packages 等功能的方案，非常适合项目级与包级混合的 monorepo

结论：submodule + turborepo 结合应该是一个比较好的方案，目前正在实践中。



## 发电前端架构演进

| 仓库             | 项目结构                                                     | 优点                                                         | 缺点                                                         |
| ---------------- | ------------------------------------------------------------ | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 中广核           | 多项目集中在一个仓库内，通过编译变量 PROJECT_KEY 按路由进行区分打包 | 代码复用、开新项目都简单直接                                 | 1. 不考虑项目之间的依赖差异，默认所有项目的依赖都相同<br />2. 都依赖同一个 umi 项目，所以无法同时编译 |
| 辅助决策(旧仓库) | 不适合跟中广核的项目放在一起，所以建了新仓库，但项目基本沿用了中广核的架构 | 同上                                                         | 1. 同上<br />2. 树形的项目结构带来了更多的混乱<br />3. 项目的依赖已经到了有严重差异的地步<br />4. 项目需求也由于产品经理不同出现了较大差异<br />5. 项目间相互独立的趋势大于统一<br />6. Git 仓库分支管理较混乱 |
| 集团侧(旧仓库)   | 基于 yarn workspace 的 monorepo                              | 尝试将每个项目都划分为一个完整的 umi 项目，项目间的依赖、配置、编译完全独立 | 1. Git 仓库分支管理非常混乱<br />2. yarn workspace 与 umi 不太兼容，为了正常运行需要配置 yarn nohoist，导致依赖包重复安装<br />3. 依赖包重复安装导致构建缓慢及磁盘空间占用过多 |
| Monoliths        | pnpm workspace + git submodules                              | 1. 各项目拥有单独的 git 仓库，各自独立管理<br>2. 各项目的依赖、配置、编译相互独立<br>3. 借助 pnpm 包管理的特性，大幅度减少依赖包磁盘空间占用<br>4. 共享代码包无需发布到远端仓库，直接修改-构建-重启，即可生效<br>5. 不同开发者维护不同的 submodules，可以只关注自己需要开发的项目<br>6. submodules 的元信息被加到了 .gitignore 中，避免了 submodules 本身的一些缺点 | 1. 把多种方案混在一起，基本没看到类似的宣传案例，需要我们自己踩坑<br>2. 由于存在多个仓库，代码提交可能需要多次提交<br>3. umi v3 与 pnpm workspace 不太兼容，安装依赖需要启用 pnpm shamefully-hoist |
| Monoliths        | pnpm workspace + git submodules + turborepo                  | 1. 同上<br>2. 借助 turbo 的 internal packages 功能，省略 ppss 的打包流程<br>3. 借助 turbo 的任务流水线功能，动态构建特定依赖包<br>4. 优化了项目组织结构，结构更清晰且扩展可能性更强 | 1. 同上<br>2. 共享包发布到源需要先手动修改 package.json 信息，然后才能发布 |



## 项目基础框架

最近，越来越多小伙伴想要尝试新的技术框架如 Vite, Umi4, Next.js 等，甚至对于一些独立的小功能，尝试 React 体系之外的框架比如 Solid, Vue, Svelte, Qwik 等也不是不行。实际上，monorepo 的一大优势就在于包与包之间具有技术框架隔离性，选用新框架是可行的，只是目前还没有进一步的实践。

#### 为什么选择了 Umi

- 跟 antd 师出同门，所以 Umi 是非常自然而然的尝试，并且尝试后觉得还不错
- Umi 给出了各方面的实践方法，而不是只给你一个简单的脚手架，这更贴近 toB 系统的需求
- Umi 宣称是蚂蚁前端研发的最佳实践，并且已经大范围应用于他们的系统
- Umi 通过插件系统去介入项目代码的前中后流程，这个想法很有潜力

#### 为什么不再选择 Umi

- Umi4 是个毫无亮点且仓促的方案，一些重大变更甚至没个说法，比如插件系统变动
- Umi4 的一些所谓新特性，像是把社区方案拿过来，用口水粘上去组成的，大可不必，有种被别人的 KPI 恶心到的感觉
- Umi 在 monorepo 中是笨拙的
  - 我们希望项目能快速抽取代码到公共包中，但我们不希望公共包依赖项目的 src/.umi
  - mfsu 有很多问题
- 现在有了更多的技术方案可以选择，我们希望拥抱新技术
- 我们已经熟悉了 Umi 中一些好的实践，可以应用到其他框架中

#### 新的框架选择哪个

我们并不会各个框架都去尝试一遍，这不利于项目的开发与维护。

Vercel 公司的 Turborepo 技术方案给我留下了深刻的印象。并且考虑到流行度的因素，下一个框架选择应该会是同样出自 Vercel 公司之手的 Next.js。

参考：

- [从 Islands Architecture 看前端有多卷 - 掘金 (juejin.cn)](https://juejin.cn/post/7130790357553381389)
- [Signal：更多前端框架的选择 - 掘金 (juejin.cn)](https://juejin.cn/post/7203266679602151482)
- [State of JavaScript 2022 (stateofjs.com)](https://2022.stateofjs.com/zh-Hans/)
- [Blog | Next.js (nextjs.org)](https://nextjs.org/blog)
