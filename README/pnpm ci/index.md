# pnpm ci

此命令用于在本地电脑构建并推送 docker 镜像，可以替代 Jenkins 在前端应用发布中的作用，避免 Jenkins 服务器拥堵带来的等待问题，有助于早点下班！

![](./158C4098-79EB-4960-859F-7D34E7B2480A.png)

### 使用示例

`pnpm ci <app_name>`

### 命令使用须知

- 你的电脑需要安装任意 Docker 引擎（因为版权问题不要安装 Docker Desktop）
  - Podman <https://podman.io/>
  - OrbStack <https://orbstack.dev/>
- 需要在 `apps/<app_name>` 项目根路径添加文件 `ci.toml`
- 此命令只做应用构建、镜像构建、镜像推送、Rainbond 触发，其他的操作如切换分支、修改 .env 文件、清除缓存、安装依赖等需要你自己手动操作
- 使用前请务必确认 app、ppss、pps-style 的分支

### ci.toml 文件

文件需要以下信息，可以从 [builder-script](http://git.tsintergy.com:8070/pps/build/builder-script) 中找到并复制

```toml
Dockerfile = ''
docker_image_name = ''
docker_image_tag = ''
rainbond_deploy_trigger = true
rainbond_deploy_api = ''
rainbond_deploy_key = ''
```

### .env 文件

.env 文件是非必须的，用于传递一些特殊的变量给到应用构建步骤，目前约定的变量有两个，以后数量可能还会增加。

- UMI_ENV，用于修改 [环境变量 (umijs.org)](https://umijs.org/docs/guides/env-variables#umi_env)，实现额外配置文件的加载
- ROUTER_BASE，用于修改 [base](https://umijs.org/docs/api/config#base) 及 [publicPath](https://umijs.org/docs/api/config#publicpath)，支持特殊的路由前缀配置

参考配置，不需要的变量可以忽略

```env
UMI_ENV=huaneng
ROUTER_BASE=/pc-01/PublicDataManage/
```

### 效率对比

下面截图是本地电脑 ci 对比 Jenkins ci，效率差别非常明显，当然，也跟你的电脑性能有关系。

![](./Snipaste_2023-10-31_16-14-09.jpg)

![](./Snipaste_2023-10-31_16-14-31.jpg)
