# Turbo相关问题记录

记录目前遇到的问题，小问题啊，耗子尾汁。

### pnpm

建议统一使用 `pnpm@7.23.0`，特别注意不要使用 [deprecated 版本](https://npmmirror.com/package/pnpm)
![](./img/Snipaste_2023-05-16_16-00-58.jpg)



### pnpm clone 无法执行

所有脚本都需要 `bash` 环境。

Windows 电脑请使用 `git bash`，可以执行 `pnpm clone $app_name` 以输入项目名的方式去避免选择名称的交互，但更好的方式是在 VS Code 的 Terminal 配置 `git bash` 去使用。



### eslint 失效与提交报错

是项目的 eslint 版本与根仓库的 eslint 版本不一致导致的，我大意了升级的时候没发现这个问题。

解决：删除项目的以下依赖

```json
"@tsintergy/eslint-config-react"
"eslint"
"stylelint"
"prettier"
```



### cannot read properties of undefined (reading 'undefined')

![](./img/A45180A5-E9C0-4444-8706-C744D8F54496.png)

解决：当前 git 分支没有 turbo 升级相关的提交，需要自己把相关提交 cherry-pick 到当前分支。



### configuration[0].module.rules[10].resolve has an unknown property 'fullySpecified'. These properties are valid

![](./img/08154270-B848-45CA-AD69-11F5B4566B33.png)

解决：删除项目 `config/config.ts` 中的

```ts
config.module
      .rule('mjs-rule')
      .test(/.m?js/)
      .resolve.set('fullySpecified', false);
```



### WARNING  failed to contact turbod. Continuing in standalone mode: connection to turbo daemon process failed

![](./img/F15B2400-D220-4b76-9399-FE42733A8DB4.png)

解决：是 Turborepo 的 bug，导致有文件被锁了，手动删除文件夹即可

```txt
Windows: C:\Users\<USER>\AppData\Local\Temp\turbod
```



### Failed to add workspace "sx-group" from apps/mx-group, it already exists at apps/sx-group

![](./img/Snipaste_2023-05-16_15-53-23.jpg)

解决：存在多个项目的 package.json name 重名，要改成与项目名一致才行。通常也是因为当前分支没有 turbo 相关的升级提交，因为在升级时我已经统一改过包重名的问题了，但也有一些项目不在巨石仓库组内所以我没有改。
