# pnpm swagger

> **Tip:** 请使用 VS Code 插件替代这个脚本 <https://marketplace.visualstudio.com/items?itemName=zoffyzhang.tsie-openapi-port>

将整个 swagger 服务的接口定义保存为本地 ts 文件，可以节省大量写接口字段定义的时间。

自动生成的接口定义不是完美的，因为 swagger 中的信息本身也没有那么严谨，实际使用的时候你可能需要自己套一层 `Required/Partial` 之类的定义修饰，但至少字段名、字段注释这些是正确的，依然值得使用。


### 使用示例

`pnpm swagger <app_name> <swagger 服务地址>`

### 命令使用须知

- swagger 服务地址参数只要从浏览器复制你使用的 swagger 地址就行
- 生成的 ts 文件路径为 `apps/<app>/src/types/swagger.ts`，不要手动修改里面的内容
- 接口定义有变动的话，再次使用命令去更新 `swagger.ts` 即可
- 此命令是对 [openapi-typescript](https://www.npmjs.com/package/openapi-typescript) 的简化使用

### 使用截图

![](./CleanShot%202023-11-30%20at%2011.42.08.jpg)

![](./Snipaste_2023-10-31_18-13-48.jpg)
