# Turbo架构改动说明

- [Turborepo](https://turbo.build/repo) 加持，各项目直接使用 ppss 的源码，省略 ppss 的单独打包过程

- git 仓库组织改进
  - packages 拆分为 apps + packages
  - 由于远端源也改动了，所以需要重新下载仓库

- 各项目的升级已经提交在 `master` 或 `dev`，需要自己 cherry-pick 到其他分支

- pnpm 版本限定为 <=7.23.0，大于这个版本目前不兼容

- 抽取统一维护的 Jenkins 构建脚本

  - 代码检出方式改为 checkout，checkout 参数可以为 branch、commit、tag
  - 支持 app_checkout
  - 支持 ppss_checkout
  - 支持 pps_style_checkout

- Stylelint 升级与配置修复

- 添加了 vscode workspace 配置，现在可以通过 `⌘+p` 或 `ctrl+p` 快速打开文件

- mfsu 没有把 pps-style 识别为一个包，建议关闭

- 代理配置
  - 各项目的代理配置各自配置
  - 代理地址统一维护（非强制）
  - 去除了 PROXY_KEY，改为 PROXY_URL
  - 通过 umi 插件改进了代理信息提示

- ppss
  - 放弃了 ppss 的 peerDependencies 管理，放弃了硬链接做法，改回软链接
  - 在 monorepo 中，通过软链接指向同一个 npm 包，比 peerDependencies 管理更加合理
  - 因为上述原因，ppss 不再支持 echarts4，仅支持 echarts5
  - 文档尝试了升级 dumi2 和换成 Modern.js Doc，但都太麻烦了，所以文档的启动和发布还是保持原状，但需要单独 `git clone http://git.tsintergy.com:8070/pps/frontend-monoliths/packages/ppss` 出来去启动和发布

- echarts

  - 各项目的 echarts 版本已经统一修改为 `"echarts": "5.x"`，与 ppss 保持一致，避免重复打包

  - 建了个新的包 `@tsintergy/geo-json` 用来存放地图文件

  - 以下项目有地图代码相关的修改，可能需要留意一下，我只做了大致的检查

    - gd-fire
    - gd-group
    - gs-newenergy
    - mx-group
    - province
    - sx-newenergy

  - 升级到 v5 的操作

    - 依赖改为 `"echarts": "5.x"`，删除依赖 `"@types/echarts"`

    - 把项目里的 `import echarts` 改为 `import * as echarts`

    - 把地图注册相关的代码改为

      ```js
      import * as echarts from 'echarts';
      import geoJson from '@tsintergy/geo-json/echarts/地图.json'

      echarts.registerMap('名称', geoJson);
      ```

    - 还有一些零零散散的修改，比如大屏页面的背景色改为透明色等
