/* eslint-disable no-console */
import type { IApi } from '@umijs/types';
import { chalk } from '@umijs/utils';

const { proxyUrlMap } = require('../../../../scripts/meta');

const { cyan, white, dim } = chalk;
const { PROXY_URL } = process.env;

export default (api: IApi) => {
  if (api.env !== 'development') {
    return;
  }

  api.onDevCompileDone(({ isFirstCompile }) => {
    if (!isFirstCompile) {
      return;
    }
    const proxyName = Object.entries(proxyUrlMap).find((item) => item[1] === PROXY_URL)?.[0];
    const proxyToTest = PROXY_URL === proxyUrlMap.测试环境;
    const proxyToGdtTest = PROXY_URL === proxyUrlMap.国电投测试环境;
    console.log(white`\n  代理信息: `);
    console.log(
      white('  - 当前代理: '),
      chalk.green(proxyName),
      dim(proxyToGdtTest || proxyToTest ? '(开发/测试环境直连请先查看 README)' : ''),
    );
    const port = api.getPort();
    if (proxyToGdtTest || proxyToTest) {
      if (proxyToTest) {
        console.log(
          white('  - 登录地址: '),
          cyan(`https://adssx-test-gzdevops3.tsintergy.com/usercenter/#/login`),
        );
        console.log(
          white('  - 本机服务: '),
          cyan(`https://dev.adssx-test-gzdevops3.tsintergy.com:${port}`),
        );
      } else if (proxyToGdtTest) {
        console.log(
          white('  - 登录地址: '),
          cyan(`http://gdt.test.gzdevops3.tsintergy.com/usercenter/#/login`),
        );
        console.log(
          white('  - 本机服务: '),
          cyan(`http://dev.gdt.test.gzdevops3.tsintergy.com:${port}`),
        );
      } else {
        console.log(white('  - 服务地址: '), cyan(`http://localhost:${port}`));
      }
    }
    console.log();
  });
};
