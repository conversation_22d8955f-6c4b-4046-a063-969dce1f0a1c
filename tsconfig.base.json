{"compilerOptions": {"outDir": "./outDir", "jsx": "react-jsx", "module": "ESNext", "target": "ESNext", "lib": ["ESNext", "DOM"], "moduleResolution": "Node", "strict": true, "allowJs": true, "checkJs": true, "noEmit": true, "sourceMap": true, "skipLibCheck": true, "importHelpers": true, "noUnusedLocals": true, "esModuleInterop": true, "noImplicitReturns": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true}, "exclude": ["**/node_modules", "**/lib", "**/dist", "**/__test__", "**/test", "**/docs", "**/tests", "**/assets", "**/public", "**/.umi", "**/.umi-production"]}