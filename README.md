# Monoliths

发电前端巨石阵，keep coding and debug on!

![stonehenge](./README/root/stonehenge.jpg)

## 开始

以下命令需要 `bash` 环境，Windows 电脑请使用 `git bash`

```bash
# 安装全局工具
npm i -g zx commitizen
# 安装特定版本 pnpm
npm/pnpm i -g pnpm@7.23.0
# 下载根仓库
git clone http://git.tsintergy.com:8070/pps/frontend-monoliths/root monoliths
cd monoliths
# 安装根仓库依赖
pnpm i
```

```bash
# 下载需要的子仓库
pnpm clone <app_name>
# 安装依赖，软链接本地 packages 到 apps
pnpm i
# 启动子仓库
pnpm dev <app_name>
```

## 注意事项

- ppss 是一个可以独立打包的 npm 包，所以应该 `import {xxx} from 'ppss'`，而不应该 import 包里面的文件
- pps-style 并不需要依赖或者打包，所以可以直接 import 里面的文件
- ppss 包已有代码的改动应该尽量小心，避免破坏其他项目的业务逻辑
- 禁止 apps 间相互 import 代码，这会带来严重的依赖问题
- 函数级、组件级的代码复用应该写到 ppss，然后在项目中使用 ppss 的代码
- 后续可能会使用微前端去解决项目间的页面级复用

## 开发/测试环境直连

需要修改 host，建议使用软件 [SwitchHosts](https://switchhosts.vercel.app/zh)

添加 host 配置

```bash
# 开发环境
127.0.0.1 dev.adssx-dev-gzdevops.tsintergy.com
# 测试环境
127.0.0.1 dev.adssx-test-gzdevops.tsintergy.com
127.0.0.1 dev.adssx-test-gzdevops3.tsintergy.com
# 国电投测试
127.0.0.1 dev.gdt.test.gzdevops3.tsintergy.com
```

对于开发环境和测试环境直连，dev 脚本只是加了个[环境变量](https://v3.umijs.org/zh-CN/docs/env-variables#https)去开启 umi 的 https 服务，如果你不嫌麻烦可以在自己电脑生成根证书去导入并信任（注意不要导入别人给你的证书），但这是没什么必要的。

网络连接错误的问题都跟服务端的 HSTS 设置有关，如果遇到可以尝试在浏览器禁用直连服务的 HSTS

1. 参考问题 <https://stackoverflow.com/questions/63825407/microsoft-edge-redirects-http-localhost-to-https-localhost>
2. 打开地址 <chrome://net-internals/#hsts>
3. 对于测试环境，输入并删除 `adssx-test-gzdevops3.tsintergy.com`，其他环境同理

## 构建与部署

以山西火电集团侧项目 sx-group 为例

1. Jenkins 脚本 <http://git.tsintergy.com:8070/pps/build/builder-script/blob/master/adssx/group/sx/frontend/Jenkinsfile_web.groovy>
2. Jenkins 任务 <http://dev.jenkins.tsintergy.com:9080/jenkins/job/IT_ADSSX_SX_FIRE/job/IT_ADSSX_SX_GROUP_FRONTEND/>
3. Rainbond 测试环境 <http://rainbond.gzdevops.tsintergy.com/#/team/56c3io20/region/fadianjiqunzuixin/components/gr57a2a3/overview>

另外，如果需要频繁发布，Jenkins 的效率是不尽人意的，此时可以通过本地构建代替 Jenkins 构建，详细请查看文档 [pnpm ci](./README/pnpm%20ci/index.md)

## 更多文档

- [旧项目迁移指南](./README/旧项目迁移指南/旧项目迁移指南.md)
- [论发电前端架构](./README/论发电前端架构.md)
- [Turbo架构改动说明](./README/Turbo架构改动说明.md)
- [Turbo相关问题记录](./README/Turbo相关问题记录/index.md)
- [pnpm ci](./README/pnpm%20ci/index.md)
- [pnpm swagger](./README/pnpm%20swagger/index.md)
