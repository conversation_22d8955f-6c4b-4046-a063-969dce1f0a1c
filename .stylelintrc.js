module.exports = {
  extends: [
    'stylelint-config-standard',
    'stylelint-config-rational-order',
    'stylelint-config-css-modules',
    'stylelint-config-prettier',
  ],
  customSyntax: 'postcss-less',
  rules: {
    'selector-class-pattern': null,
    'import-notation': null,
    'font-family-no-missing-generic-family-keyword': null,
    'no-descending-specificity': null,
    'custom-property-pattern': null,
    'declaration-property-value-no-unknown': true,
    'declaration-block-no-redundant-longhand-properties': null,
  },
  ignoreFiles: ['**/*.js', '**/*.jsx', '**/*.tsx', '**/*.ts'],
};
