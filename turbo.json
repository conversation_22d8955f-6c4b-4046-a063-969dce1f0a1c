{"$schema": "https://turbo.build/schema.json", "ui": "tui", "dangerouslyDisablePackageManagerCheck": true, "tasks": {"turbo:static": {"cache": false}, "turbo:build": {"inputs": ["src/**", "!src/.umi*", "public/**", "scripts/**", "../../scripts/**", "package.json", "tsconfig.json", ".fatherrc.ts", "tsup.config.ts"], "outputs": ["dist/**"]}, "dev": {"dependsOn": ["^turbo:static", "^turbo:build"], "cache": false, "persistent": true}, "build": {"dependsOn": ["^turbo:static", "^turbo:build"], "cache": false}, "build@pct1": {"dependsOn": ["^turbo:static", "^turbo:build"], "cache": false}}}